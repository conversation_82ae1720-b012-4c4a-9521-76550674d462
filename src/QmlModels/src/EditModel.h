#pragma once

#include <QObject>
#include <QList>
#include <QString>
#include <memory>

#include "TrackModel.h"
#include "SelectionManagerModel.h"
#include "EditViewModel.h"

#include "TracktionWrapper/EditWrapper.h"

class TrackWrapper;

class EditModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ getName NOTIFY nameChanged)
    Q_PROPERTY(QList<QObject*> tracks READ getTracks NOTIFY tracksChanged)
    Q_PROPERTY(int projectLengthInBars READ getProjectLengthInBars NOTIFY projectLengthInBarsChanged)
    Q_PROPERTY(bool isRecording READ getIsRecording NOTIFY isRecordingChanged)
    Q_PROPERTY(bool isItemSelected READ getIsItemSelected NOTIFY isItemSelectedChanged)
    Q_PROPERTY(bool drawWaveforms READ getDrawWaveforms WRITE setDrawWaveforms NOTIFY drawWaveformsChanged)
    Q_PROPERTY(int timeSignatureNumerator READ getTimeSignatureNumerator NOTIFY timeSignatureChanged)
    Q_PROPERTY(int timeSignatureDenominator READ getTimeSignatureDenominator NOTIFY timeSignatureChanged)
    Q_PROPERTY(bool hasValidEdit READ hasValidEdit NOTIFY editValidityChanged)

public:
    explicit EditModel(EditWrapper* editWrapper,
                       SelectionManagerModel* selectionManagerModel,
                       EditViewModel* editViewModel,
                       QObject* parent = nullptr);
    ~EditModel() override;

    // Property getters
    QString getName() const;
    QList<QObject*> getTracks() const;
    int getProjectLengthInBars() const;
    bool getIsRecording() const;
    bool getIsItemSelected() const;
    bool getDrawWaveforms() const;
    int getTimeSignatureNumerator() const;
    int getTimeSignatureDenominator() const;
    bool hasValidEdit() const;

    Q_INVOKABLE void populateTracks();
    Q_INVOKABLE TrackModel* getTrack(int index) const;
    Q_INVOKABLE int getNumTracks() const;
    Q_INVOKABLE void addTrack();
    Q_INVOKABLE void removeTrack(TrackModel* track);

    Q_INVOKABLE void saveEdit();
    Q_INVOKABLE void toggleRecord();
    Q_INVOKABLE void addNewAudioTrack();
    Q_INVOKABLE void clearAllTracks();
    Q_INVOKABLE void undo();
    Q_INVOKABLE void redo();
    Q_INVOKABLE void moveToFirstNote();
    Q_INVOKABLE void createMidiClip();
    Q_INVOKABLE void deleteSelectedItem();
    Q_INVOKABLE void setDrawWaveforms(bool draw);
    Q_INVOKABLE void updateEditWrapper(EditWrapper* editWrapper);

    // Access methods (non-owning pointers for external use)
    EditWrapper* getEditWrapper() const;

signals:
    void nameChanged();
    void tracksChanged();
    void projectLengthInBarsChanged();
    void isRecordingChanged();
    void isItemSelectedChanged();
    void drawWaveformsChanged();
    void timeSignatureChanged();
    void editValidityChanged();

private:
    // Non-owning reference to EditWrapper (owned by EngineModel)
    EditWrapper* mEditWrapper;

    // Non-owning references to other models
    SelectionManagerModel* mSelectionManagerModel;
    EditViewModel* mEditViewModel;

    // Owned track models (improved from raw pointers)
    QList<std::unique_ptr<TrackModel>> mTracks;

    // Cached state
    int mProjectLengthInBars;
    bool mIsRecording = false;
    bool mIsItemSelected = false;
    bool mDrawWaveforms = true;

    // Helper methods
    void cleanupTracks();
    void updateCachedState();
    bool isEditWrapperValid() const;
};
