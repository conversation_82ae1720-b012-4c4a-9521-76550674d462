#pragma once

#include <QObject>
#include <QList> // For QList
#include <QString> // For QString
#include <memory> // For std::unique_ptr

#include "TrackModel.h"
#include "SelectionManagerModel.h"
#include "EditViewModel.h"

#include "TracktionWrapper/EditWrapper.h"

class TrackWrapper;

// Declare std::unique_ptr<EditWrapper> as a metatype for Qt's Meta-Object System
Q_DECLARE_METATYPE(std::unique_ptr<EditWrapper>)

class EditModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ getName NOTIFY nameChanged)
    Q_PROPERTY(QList<QObject*> tracks READ getTracks NOTIFY tracksChanged)
    Q_PROPERTY(int projectLengthInBars READ getProjectLengthInBars NOTIFY projectLengthInBarsChanged)
    Q_PROPERTY(bool isRecording READ getIsRecording NOTIFY isRecordingChanged)
    Q_PROPERTY(bool isItemSelected READ getIsItemSelected NOTIFY isItemSelectedChanged)
    Q_PROPERTY(bool drawWaveforms READ getDrawWaveforms WRITE setDrawWaveforms NOTIFY drawWaveformsChanged)
    Q_PROPERTY(int timeSignatureNumerator READ getTimeSignatureNumerator NOTIFY timeSignatureChanged)
    Q_PROPERTY(int timeSignatureDenominator READ getTimeSignatureDenominator NOTIFY timeSignatureChanged)

public:
    explicit EditModel(EditWrapper* editWrapper,
                       SelectionManagerModel* selectionManagerModel,
                       EditViewModel* editViewModel,
                       QObject* parent = nullptr);
    ~EditModel() override;

    QString getName() const;
    QList<QObject*> getTracks() const;
    int getProjectLengthInBars() const;
    bool getIsRecording() const;
    bool getIsItemSelected() const;
    bool getDrawWaveforms() const;
    int getTimeSignatureNumerator() const;
    int getTimeSignatureDenominator() const;

    Q_INVOKABLE void populateTracks();
    Q_INVOKABLE TrackModel* getTrack(int index) const;
    Q_INVOKABLE int getNumTracks() const;
    Q_INVOKABLE void addTrack();
    Q_INVOKABLE void removeTrack(TrackModel* track);

    Q_INVOKABLE void saveEdit();
    Q_INVOKABLE void toggleRecord();
    Q_INVOKABLE void addNewAudioTrack();
    Q_INVOKABLE void clearAllTracks();
    Q_INVOKABLE void undo();
    Q_INVOKABLE void redo();
    Q_INVOKABLE void moveToFirstNote();
    Q_INVOKABLE void createMidiClip();
    Q_INVOKABLE void deleteSelectedItem();
    Q_INVOKABLE void setDrawWaveforms(bool draw);

    EditWrapper* getEditWrapper() const;

signals:
    void nameChanged();
    void tracksChanged();
    void projectLengthInBarsChanged();
    void isRecordingChanged();
    void isItemSelectedChanged();
    void drawWaveformsChanged();
    void timeSignatureChanged();

private:
    EditWrapper* mEditWrapper; // Raw pointer - ownership remains with EngineModel
    SelectionManagerModel* mSelectionManagerModel;
    EditViewModel* mEditViewModel;
    QList<QObject*> mTracks;
    int mProjectLengthInBars;
    bool mIsRecording = false; // Re-introduced cached member
    bool mIsItemSelected = false; // Re-introduced cached member
    bool mDrawWaveforms = true; // Re-introduced cached member
};
