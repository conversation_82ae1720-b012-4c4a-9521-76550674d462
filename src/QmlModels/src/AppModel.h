#pragma once

#include <QObject>
#include <memory>
#include "TracktionWrapper/WrapperChangeListener.h"

// Includes for QML and JUCE classes

#include "EngineModel.h"
#include "TransportModel.h"
#include "EditModel.h"
#include "EditViewModel.h"
#include "ProjectManagerModel.h"
#include "DeviceManagerModel.h"
#include "SelectionManagerModel.h"

// Forward declarations for wrapper classes (these are not Q_PROPERTY types, so forward declaration is fine)
class EditWrapper;
class SelectionManagerWrapper;
class TransportWrapper;
class EditViewWrapper;

namespace juce { class ChangeBroadcaster; }
namespace tracktion { inline namespace engine { class Engine; class Edit; class TransportControl; class SelectionManager; } }

class AppModel : public QObject
{
    Q_OBJECT
    Q_PROPERTY(EngineModel* engineModel READ getEngineModel CONSTANT)
    Q_PROPERTY(TransportModel* transportModel READ getTransportModel NOTIFY transportModelChanged)
    Q_PROPERTY(EditModel* editModel READ getEditModel WRITE setEditModel NOTIFY editModelChanged)
    Q_PROPERTY(EditViewModel* editViewModel READ getEditViewModel CONSTANT)
    Q_PROPERTY(ProjectManagerModel* projectManagerModel READ getProjectManagerModel CONSTANT)
    Q_PROPERTY(DeviceManagerModel* deviceManagerModel READ getDeviceManagerModel CONSTANT)
    Q_PROPERTY(SelectionManagerModel* selectionManagerModel READ getSelectionManagerModel CONSTANT)

public:
    explicit AppModel(QObject* parent = nullptr);
    ~AppModel() override;

    EngineModel* getEngineModel() const { return mEngineModel.get(); }
    TransportModel* getTransportModel() const { return mTransportModel.get(); }

    EditModel* getEditModel() const
    {
        return mEditModel.get();
    }

    EditViewModel* getEditViewModel() const { return mEditViewModel.get(); }
    ProjectManagerModel* getProjectManagerModel() const { return mProjectManagerModel.get(); }
    DeviceManagerModel* getDeviceManagerModel() const { return mDeviceManagerModel.get(); }
    SelectionManagerModel* getSelectionManagerModel() const { return mSelectionManagerModel.get(); }

    void setEditModel(std::unique_ptr<EditModel> newEditModel);

signals:
    void editModelChanged();
    void transportModelChanged();

private slots:
    void handleEditLoaded(EditWrapper* newEditWrapper);

private:
    void createEditDependentModels(EditWrapper* editWrapper);

private:
    // Order for proper destruction: Edit-dependent models first, then core models, Engine last
    // Members are destructed in reverse order of declaration
    std::unique_ptr<EngineModel> mEngineModel;               // Core engine (destructed last)
    std::unique_ptr<DeviceManagerModel> mDeviceManagerModel; // Device management
    std::unique_ptr<SelectionManagerModel> mSelectionManagerModel; // Selection management
    std::unique_ptr<ProjectManagerModel> mProjectManagerModel; // Project management
    std::unique_ptr<EditViewModel> mEditViewModel;           // Edit view state
    std::unique_ptr<TransportModel> mTransportModel;         // Transport controls
    std::unique_ptr<EditModel> mEditModel;                   // Edit model (now owned by AppModel)

    // Helper methods
    void initializeCoreModels();
    void cleanupEditDependentModels();
};
