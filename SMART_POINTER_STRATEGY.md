# Smart Pointer Strategy for QML DAW Model-Wrapper Architecture

## Overview

This document defines a comprehensive strategy for consistent smart pointer usage throughout the QML DAW codebase, focusing on the Model-Wrapper architectural pattern. The goal is to eliminate ownership ambiguity, prevent memory leaks and use-after-free errors, and establish clear patterns for future development.

## Core Principles

### 1. Clear Ownership Semantics
- **Single Owner**: Use `std::unique_ptr` when one object exclusively owns another
- **Shared Ownership**: Use `std::shared_ptr` when multiple objects need to share ownership
- **Non-Owning References**: Use raw pointers or references only for non-owning relationships
- **Weak References**: Use `std::weak_ptr` to break circular dependencies

### 2. Consistent Patterns
- All Model classes should follow the same ownership patterns for similar relationships
- Wrapper classes should use consistent smart pointer patterns internally
- Signal/slot connections should be compatible with smart pointer usage

### 3. Exception Safety
- All constructors and destructors should be exception-safe
- RAII principles should be followed throughout
- Proper cleanup order should be maintained

## Ownership Rules by Component Type

### Engine and Core Components
```cpp
// EngineModel owns the core engine infrastructure
class EngineModel {
private:
    std::unique_ptr<EngineWrapper> mEngineWrapper;           // Exclusive ownership
    std::unique_ptr<EditWrapper> mCurrentEditWrapper;       // Exclusive ownership
    std::unique_ptr<GlobalTrackModel> mArrangerTrackModel;   // Exclusive ownership
    // ... other global track models
};
```

### Application-Level Models
```cpp
// AppModel owns all top-level model instances
class AppModel {
private:
    std::unique_ptr<EngineModel> mEngineModel;               // Exclusive ownership
    std::unique_ptr<DeviceManagerModel> mDeviceManagerModel; // Exclusive ownership
    std::unique_ptr<SelectionManagerModel> mSelectionManagerModel; // Exclusive ownership
    std::unique_ptr<ProjectManagerModel> mProjectManagerModel; // Exclusive ownership
    std::unique_ptr<EditViewModel> mEditViewModel;           // Exclusive ownership
    std::unique_ptr<TransportModel> mTransportModel;         // Exclusive ownership
    std::unique_ptr<EditModel> mEditModel;                   // Changed from raw pointer
};
```

### Edit-Dependent Models
```cpp
// EditModel uses non-owning references to shared resources
class EditModel {
private:
    std::weak_ptr<EditWrapper> mEditWrapper;                 // Non-owning reference
    SelectionManagerModel* mSelectionManagerModel;          // Non-owning reference
    EditViewModel* mEditViewModel;                           // Non-owning reference
    QList<std::unique_ptr<TrackModel>> mTracks;             // Owns track models
};

// TransportModel owns its wrapper
class TransportModel {
private:
    std::unique_ptr<TransportWrapper> mTransportWrapper;     // Exclusive ownership
    EditModel* mEditModel;                                   // Non-owning reference
};
```

### Track and Clip Hierarchy
```cpp
// TrackModel uses shared ownership for wrapper (multiple references possible)
class TrackModel {
private:
    std::shared_ptr<TrackWrapper> mTrack;                    // Shared ownership
    QList<std::unique_ptr<ClipModel>> mClipModels;          // Owns clip models
};

// ClipModel uses shared ownership for wrapper
class ClipModel {
private:
    std::shared_ptr<ClipWrapper> mClip;                      // Shared ownership
};
```

### Device and Selection Management
```cpp
// DeviceManagerModel uses non-owning reference (DeviceManager owned by Engine)
class DeviceManagerModel {
private:
    std::weak_ptr<tracktion::engine::DeviceManager> mDeviceManager; // Non-owning reference
};

// SelectionManagerModel owns its wrapper
class SelectionManagerModel {
private:
    std::unique_ptr<SelectionManagerWrapper> mSelectionManagerWrapper; // Exclusive ownership
};
```

## Implementation Strategy

### Phase 1: Core Infrastructure (EngineModel, AppModel)
1. Update AppModel to use `std::unique_ptr<EditModel>` instead of raw pointer
2. Implement proper destruction order with smart pointers
3. Add exception handling in constructors and destructors

### Phase 2: Edit-Dependent Models (EditModel, TransportModel)
1. Change EditModel to use `std::weak_ptr<EditWrapper>` for safer non-owning reference
2. Update EditModel to own TrackModel instances with `std::unique_ptr`
3. Ensure proper cleanup of owned resources

### Phase 3: Track and Clip Hierarchy
1. Update TrackModel to own ClipModel instances with `std::unique_ptr`
2. Maintain shared ownership of wrapper objects where appropriate
3. Implement proper parent-child relationships

### Phase 4: Device and Selection Management
1. Update DeviceManagerModel to use `std::weak_ptr` for DeviceManager reference
2. Ensure all wrapper-owning models use consistent patterns
3. Add proper null-checking for weak pointer usage

### Phase 5: Signal/Slot Compatibility
1. Update all signal/slot connections to work with smart pointers
2. Remove any remaining raw pointer parameters in signals
3. Implement proper lifetime management for signal connections

## Exception Handling Patterns

### Constructor Exception Safety
```cpp
// Example: AppModel constructor with exception safety
AppModel::AppModel(QObject* parent) : QObject(parent) {
    try {
        mEngineModel = std::make_unique<EngineModel>(this);
        mDeviceManagerModel = std::make_unique<DeviceManagerModel>(
            mEngineModel->getEngineWrapper(), this);
        // ... other initializations
    } catch (...) {
        // Cleanup already constructed objects
        // std::unique_ptr automatically handles cleanup
        throw; // Re-throw the exception
    }
}
```

### Destructor Exception Safety
```cpp
// Example: Safe destructor with exception handling
AppModel::~AppModel() {
    try {
        // Explicit destruction order
        mEditModel.reset();
        mTransportModel.reset();
        mEditViewModel.reset();
        // ... other models
        mEngineModel.reset(); // Engine last
    } catch (...) {
        // Log error but don't throw from destructor
        qWarning() << "Exception during AppModel destruction";
    }
}
```

## Weak Pointer Usage Patterns

### Safe Access Pattern
```cpp
// Example: Safe access to weak pointer
void EditModel::someMethod() {
    if (auto editWrapper = mEditWrapper.lock()) {
        // Safe to use editWrapper here
        editWrapper->someOperation();
    } else {
        // EditWrapper has been destroyed, handle gracefully
        qWarning() << "EditWrapper no longer available";
    }
}
```

## Migration Guidelines

### Step-by-Step Migration
1. **Identify Ownership**: Determine who should own each object
2. **Update Headers**: Change member variable types to appropriate smart pointers
3. **Update Constructors**: Modify initialization to use smart pointer constructors
4. **Update Destructors**: Remove manual delete calls, let smart pointers handle cleanup
5. **Update Access Methods**: Add null-checking and weak pointer locking where needed
6. **Update Signals**: Ensure signal parameters are compatible with new pointer types
7. **Test Thoroughly**: Verify no memory leaks or use-after-free errors

### Compatibility Considerations
- Maintain existing public APIs where possible
- Add overloads for smart pointer versions of methods
- Provide migration path for existing code
- Document breaking changes clearly

## Benefits

### Memory Safety
- Automatic cleanup prevents memory leaks
- RAII ensures proper resource management
- Weak pointers prevent use-after-free errors

### Code Clarity
- Clear ownership semantics
- Consistent patterns across codebase
- Self-documenting code through smart pointer types

### Maintainability
- Easier to reason about object lifetimes
- Reduced debugging time for memory issues
- Better support for future refactoring

## Testing Strategy

### Unit Tests
- Test object construction and destruction
- Verify proper cleanup in exception scenarios
- Test weak pointer behavior when referenced object is destroyed

### Integration Tests
- Test complete application startup and shutdown
- Verify proper destruction order
- Test edit loading and unloading scenarios

### Memory Testing
- Use AddressSanitizer to detect memory errors
- Run Valgrind or similar tools to check for leaks
- Stress test with multiple edit load/unload cycles
